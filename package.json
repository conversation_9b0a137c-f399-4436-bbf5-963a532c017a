{"name": "promptrepo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "initdb": "npx drizzle-kit push"}, "dependencies": {"@ai-sdk/openai": "^2.0.0", "@ai-sdk/react": "^2.0.8", "@modelcontextprotocol/sdk": "^1.12.0", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.80.6", "@vercel/analytics": "^1.5.0", "ai": "^5.0.8", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "fetch-to-node": "^2.1.0", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lucide-react": "^0.511.0", "next": "15.3.2", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.42", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.80.6", "@types/node": "^20", "@types/pg": "^8.15.2", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}